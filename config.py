from dataclasses import dataclass, field
from typing import Dict, List, Tuple

@dataclass
class AppConfig:
    demo_source: str = "webcam"
    camera_id: int = 0
    config_path: str = "exp/GFL_0503_gpu2/train_config.yml"
    model_path: str = "exp/GFL_0503_gpu2/model_best_score/model_best_score.pth"
    size_ranges_config: str = "pixel_to_physical_py/config/size_ranges.yaml"
    distance_table_path: str = "pixel_to_physical_py/config/distance_table"
    calibration_mapx_path: str = "pixel_to_physical_py/config/mapx"
    calibration_mapy_path: str = "pixel_to_physical_py/config/mapy"
    calib_yaml_path: str = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    output_dir: str = "imgs/"
    score_thres = 0.3
    # 从 pixel_converter.py 移动过来的常量
    imgx_range: Tuple[int, int] = (30, 1250)    # IMGX_RANGE
    imgy_range: Tuple[int, int] = (420, 700)    # IMGY_RANGE 标定范围
    X_MIN: int = 0                              # X_MIN
    X_MAX: int = 75                             # X_MAX
    Y_MIN: int = -125                           # Y_MIN
    Y_MAX: int = 125                            # Y_MAX

    img_wh: Tuple[int, int] = field(default_factory=lambda: (720, 1280))
    class_names: List[str] = field(
        default_factory=lambda: [
            'Trash can', 'Charging dock', 'Cleaning cloth', 'Rug', 'Shoes',
            'Wire', 'Sliding rail', 'Wheels'
        ]
    )
