import yaml
import numpy as np
from dataclasses import dataclass
from typing import Tuple
from src.pixel_converter import PixelConverter

@dataclass
class BBox:
    x1: int
    y1: int
    x2: int
    y2: int

@dataclass
class DetectionResult:
    bbox: BBox
    label: str
    confidence: float
    physical_distance: float = 0.0
    left_distance: float = 0.0
    right_distance: float = 0.0
    length: float = 0.0

class DetectionProcessor:
    def __init__(self):
        self.size_ranges_config = None

    def set_size_ranges_config(self, config):
        """Set the size ranges configuration from YAML."""
        self.size_ranges_config = config

    def is_size_reasonable_for_label(self, label: str, size_y: float) -> bool:
        """Check if the object size is reasonable for its label."""
        try:
            object_config = self.size_ranges_config["objects"].get(label, self.size_ranges_config["default"])
            
            config_max_size = object_config["max_size"]
            config_min_size = object_config["min_size"]
            description = object_config["description"]

            if size_y > config_max_size or size_y < config_min_size:
                print(f"{label}长度:{size_y}")
                print(f"警告：{description}")
                return False
            return True
        except Exception as e:
            print(f"Error checking size ranges: {str(e)}")
            return False
    def is_valid_coordinate(self, x: float, y: float) -> bool:
        """检查物理坐标是否有效（不是无效区域标记）"""
        # 检查是否为无效区域标记 (0.000, 0.490) 或类似的值
        return not (abs(x) < 0.01 and abs(y - 0.49) < 0.1)
    
    def find_nearest_valid_point(self, converter: PixelConverter, pixel_x: int, pixel_y: int, max_search_radius: int = 50) -> Tuple[float, float]:
        """在给定像素点周围搜索最近的有效物理坐标点"""
        print(f"🔍 搜索像素点 ({pixel_x}, {pixel_y}) 周围的有效坐标...")

        for radius in range(1, max_search_radius + 1):
            # 搜索以当前点为中心的正方形区域
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    # 只检查边界点，避免重复搜索内部点
                    if abs(dx) != radius and abs(dy) != radius:
                        continue

                    search_x = pixel_x + dx
                    search_y = pixel_y + dy

                    # 检查搜索点是否在有效范围内
                    if converter.is_valid_pixel_coordinate(search_x, search_y):
                        try:
                            phys_x, phys_y = converter.query_physical_location(search_x, search_y)
                            if self.is_valid_coordinate(phys_x, phys_y):
                                print(f"✅ 找到有效点: 像素({search_x}, {search_y}) -> 物理({phys_x:.3f}, {phys_y:.3f}), 搜索半径: {radius}")
                                return phys_x, phys_y
                        except:
                            continue

        print(f"❌ 在半径 {max_search_radius} 内未找到有效坐标点")
        return None, None


    def get_robust_physical_coordinate(self, converter: PixelConverter, pixel_x: int, pixel_y: int) -> Tuple[float, float]:
        """获取鲁棒的物理坐标，处理无效区域"""
        try:
            # 首先尝试直接查询
            phys_x, phys_y = converter.query_physical_location(pixel_x, pixel_y)

            if self.is_valid_coordinate(phys_x, phys_y):
                return phys_x, phys_y

            print(f"⚠️  像素点 ({pixel_x}, {pixel_y}) 在无效标定区域，物理坐标: ({phys_x:.3f}, {phys_y:.3f})")

            # 策略1: 搜索最近的有效点
            valid_x, valid_y = self.find_nearest_valid_point(converter, pixel_x, pixel_y)
            if valid_x is not None:
                return valid_x, valid_y
            else:
                return None, None

            # # 策略2: 通过插值估算
            # interp_x, interp_y = self.interpolate_from_valid_neighbors(converter, pixel_x, pixel_y)
            # if interp_x is not None:
            #     return interp_x, interp_y

            # # 策略3: 使用经验估算（基于Y坐标的深度估算）
            # print(f"🔧 使用经验估算方法...")
            # estimated_depth = self.estimate_depth_from_y_coordinate(pixel_y)
            # print(f"✅ 经验估算深度: {estimated_depth:.3f} cm")
            # return estimated_depth, 0.0  # Y坐标设为0（中心线）

        except Exception as e:
            print(f"❌ 获取物理坐标失败: {e}")
            return None, None

    def calculate_target_size(self, bbox: BBox, converter: PixelConverter) -> Tuple[float, float, float, float]:
        """Calculate the target size and return size_y, size_x, z1, z2."""
        try:
            # Get physical coordinates for the corners
            # z1, y1 = converter.query_physical_location(bbox.x1, bbox.y2)  # bottom left
            # z2, y2 = converter.query_physical_location(bbox.x2, bbox.y2)  # bottom right
            # 使用鲁棒方法获取物理坐标
            z1, y1 = self.get_robust_physical_coordinate(converter, bbox.x1, bbox.y2)  # bottom left
            z2, y2 = self.get_robust_physical_coordinate(converter, bbox.x2, bbox.y2)  # bottom right
            
            # x3, y3 = converter.query_physical_location(bbox.x1, bbox.y1)  # top left

            # Print physical coordinates
            print(f"左下角像素坐标 ({bbox.x1}, {bbox.y2}) -> 物理坐标 ({z1:.3f}, {y1:.3f})")
            print(f"右下角像素坐标 ({bbox.x2}, {bbox.y2}) -> 物理坐标 ({z2:.3f}, {y2:.3f})")
            # print(f"像素坐标 ({bbox.x1}, {bbox.y1}) -> 物理坐标 ({x3:.3f}, {y3:.3f})")

            # Calculate lengths
            bottom_length = np.sqrt((z2 - z1)**2 + (y2 - y1)**2)
            # left_length = np.sqrt((x3 - x1)**2 + (y3 - y1)**2)

            print("----------------------------------------")
            print(f"下边长度: {bottom_length:.3f} cm")
            # print(f"左边宽度: {left_length:.3f} cm")
            print("----------------------------------------")

            return bottom_length, z1, z2

        except Exception as e:
            print(f"Error calculating target size: {str(e)}")
            return 0.0, 0.0, 0.0, 0.0

    def is_detection_reasonable(self, det: DetectionResult, table_path: str) -> Tuple[bool, float, float]:
        """Check if the detection result is reasonable."""
        if det is None:
            print("警告：检测结果为空!")
            return False, 0.0, 0.0, 0.0

        converter = PixelConverter(table_path)

        # Check if box is in image range
        if not converter.is_valid_pixel_coordinate(det.bbox.x1, det.bbox.y2) or \
           not converter.is_valid_pixel_coordinate(det.bbox.x2, det.bbox.y2):
            # print(det.bbox.x1, det.bbox.y1, det.bbox.x2, det.bbox.y2)
            print("警告：检测框超出图像范围")
            return False, 0.0, 0.0, 0.0

        # Check if box is too small
        box_area = (det.bbox.x2 - det.bbox.x1) * (det.bbox.y2 - det.bbox.y1)
        if box_area < 10:
            print(f"警告：检测框面积过小 ({box_area} 像素)")
            return False, 0.0, 0.0, 0.0

        # Calculate target size
        size_y, z1, z2 = self.calculate_target_size(det.bbox, converter)
        if size_y == 0.0:
            print("警告：无法计算目标尺寸")
            return False, 0.0, 0.0, 0.0
        
        # Check if distance is reasonable
        # distance = abs(size_x)
        distance = z1
        if distance > converter.X_MAX:
            print(f"警告：目标距离过远 ({distance:.1f} cm)")
            return False, 0.0, 0.0, 0.0

        # Print target size
        print(f"\n目标尺寸:")
        print(f"长度: {size_y:.3f} cm")
        # print(f"宽度: {size_x:.3f} cm")

        # Check size against label
        # max_size = max(size_x, size_y)
        # min_size = min(size_x, size_y)
        if not self.is_size_reasonable_for_label(det.label, size_y):
            return False, 0.0, 0.0, 0.0

        return True, z1, z2, size_y

    def process_detection_result(self, det: DetectionResult, table_path: str):
        """Process the detection result and update physical distances."""
        print("\n处理检测结果:")
        print(f"标签: {det.label}, 置信度: {det.confidence}")
        print("----------------------------------------")

        is_reasonable, z1, z2, size_y = self.is_detection_reasonable(det, table_path)
        # if not is_reasonable:
        #     print("检测结果不合理，跳过处理")
        #     return

        det.length = size_y
        # Update detection result with physical distances
        det.left_distance = z1
        det.right_distance = z2
        det.physical_distance = (z1 + z2) / 2  # Use average of left and right distances 